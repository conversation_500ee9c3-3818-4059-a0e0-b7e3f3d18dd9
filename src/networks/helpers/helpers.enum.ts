export enum SupportedNetworks {
  ETHEREUM_MAINNET = 'ethereum-mainnet',
  ETHEREUM_SEPOLIA = 'ethereum-sepolia',

  SOLANA_MAINNET = 'solana-mainnet',
  SOLANA_DEVNET = 'solana-devnet',

  BSC_MAINNET = 'bsc-mainnet',
  BSC_TESTNET = 'bsc-testnet',

  POLYGON_MAINNET = 'polygon-mainnet',
  POLYGON_MUMBAI = 'polygon-mumbai',

  BASE_MAINNET = 'base-mainnet',
  BASE_SEPOLIA = 'base-sepolia',

  ARBITRUM_MAINNET = 'arbitrum-mainnet',
  ARBITRUM_SEPOLIA = 'arbitrum-sepolia',
}
