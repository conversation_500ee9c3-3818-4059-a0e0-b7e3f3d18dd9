import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NetworksService } from './networks.service';
import { NetworksController } from './networks.controller';
import { Network } from './entities/network.entity';
import { HttpModule } from '@nestjs/axios';
import { NetworkHelperService } from './network.helper.service';
import { HelpersModule } from './helpers/helpers.module';
import { NetworkSeeder } from 'common/seeder/network.seeder';

@Module({
  imports: [TypeOrmModule.forFeature([Network]), HttpModule, HelpersModule],
  controllers: [NetworksController],
  providers: [NetworksService, NetworkHelperService],
  exports: [NetworksService, NetworkHelperService , NetworkSeeder],
})
export class NetworksModule {}
